import xgboost as xgb
import pandas as pd
import numpy as np

from models.trm.trmV2 import prepare_stock_data

# 定义特征名称（去掉target，因为它不是输入特征）
feature_names = [
    'price_range', 'price_pos', 'ma_ratio', 'volume_ratio', 'atr', 'rsi',
    'is_new_high_20', 'is_new_low_20', 'upper_shadow_pct', 'lower_shadow_pct',
    'close_open_pct', 'up_k_count', 'down_k_count', 'turnover_ratio', 'fvg_up', 'fvg_down'
]

model = xgb.XGBClassifier(random_state=42)

X, y, input_dim, dates = prepare_stock_data('./big_data/AAPL_US_5min.csv', seq_len=200)

# 将3D数组转换为2D数组以适配XGBoost
# 原始形状: [样本数, seq_len, 特征数] -> [样本数 * seq_len, 特征数]
print(f"原始X形状: {X.shape}")
print(f"原始y形状: {y.shape}")

# 重塑X: 将样本数和序列长度合并为一个维度
X_reshaped = X.reshape(-1, X.shape[-1])  # (样本数*seq_len, 特征数)
print(f"重塑后X形状: {X_reshaped.shape}")

# 重塑y: 每个样本的标签重复seq_len次
y_reshaped = np.repeat(y, X.shape[1])  # 每个标签重复200次
print(f"重塑后y形状: {y_reshaped.shape}")

# 为了加快训练速度，我们采样一部分数据
sample_size = min(50000, len(X_reshaped))  # 最多使用5万个样本
print(f"采样 {sample_size} 个样本进行训练...")

# 简单随机采样，避免分层采样可能的问题
indices = np.random.choice(len(X_reshaped), size=sample_size, replace=False)
X_sample = X_reshaped[indices]
y_sample = y_reshaped[indices]

print(f"采样后数据形状: X={X_sample.shape}, y={y_sample.shape}")
print(f"采样后标签分布: {np.bincount(y_sample.astype(int))}")

model.fit(X_sample, y_sample)

importances = model.feature_importances_
importance_df = pd.DataFrame({'feature': feature_names, 'importance': importances})
importance_df.sort_values(by='importance', ascending=False, inplace=True)
print("\n特征重要性排序:")
print(importance_df)
