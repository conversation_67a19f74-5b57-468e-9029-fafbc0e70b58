import xgboost as xgb
import pandas as pd
import numpy as np

from models.trm.trmV2 import prepare_stock_data

# 定义特征名称（去掉target，因为它不是输入特征）
feature_names = [
    'price_range', 'price_pos', 'ma_ratio', 'volume_ratio', 'atr', 'rsi',
    'is_new_high_20', 'is_new_low_20', 'upper_shadow_pct', 'lower_shadow_pct',
    'close_open_pct', 'up_k_count', 'down_k_count', 'turnover_ratio', 'fvg_up', 'fvg_down'
]

model = xgb.XGBClassifier(random_state=42)

X, y, input_dim, dates = prepare_stock_data('./big_data/AAPL_US_5min.csv', seq_len=200)

# 将3D数组转换为2D数组以适配XGBoost
# 方案1: 只使用最后一个时间步的特征（最接近预测时点）
print(f"原始X形状: {X.shape}")
print(f"原始y形状: {y.shape}")

# 提取最后一个时间步的特征
X_last_step = X[:, -1, :]  # 形状: (样本数, 特征数)
print(f"使用最后时间步X形状: {X_last_step.shape}")
print(f"对应y形状: {y.shape}")

print("这种方式保持了时间序列的逻辑性：")
print("- 每个样本使用最接近预测时点的特征")
print("- 避免了时间关系被打乱的问题")
print("- 标签与特征时间点匹配")

model.fit(X_last_step, y)

importances = model.feature_importances_
importance_df = pd.DataFrame({'feature': feature_names, 'importance': importances})
importance_df.sort_values(by='importance', ascending=False, inplace=True)
print("\n特征重要性排序:")
print(importance_df)
