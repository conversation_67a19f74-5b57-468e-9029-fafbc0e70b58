import xgboost as xgb
import pandas as pd

from models.trm.trmV2 import prepare_stock_data
model = xgb.XGBClassifier()

X, y, input_dim, dates = prepare_stock_data('./big_data/AAPL_US_5min.csv', seq_len=200)
model.fit(X, y)

importances = model.feature_importances_
feature_names = X.columns
importance_df = pd.DataFrame({'feature': feature_names, 'importance': importances})
importance_df.sort_values(by='importance', ascending=False, inplace=True)
print(importance_df)
