import xgboost as xgb
import pandas as pd
import numpy as np

from models.trm.trmV2 import prepare_stock_data

# 定义特征名称
feature_names = [
    'price_range', 'price_pos', 'ma_ratio', 'volume_ratio', 'atr', 'rsi',
    'is_new_high_20', 'is_new_low_20', 'upper_shadow_pct', 'lower_shadow_pct', 
    'close_open_pct', 'up_k_count', 'down_k_count', 'turnover_ratio', 'fvg_up', 'fvg_down'
]

print("=== 股票特征重要性分析 ===")
print("加载数据...")

X, y, input_dim, dates = prepare_stock_data('./big_data/AAPL_US_5min.csv', seq_len=200)
print(f"原始数据形状: X={X.shape}, y={y.shape}")

# 方案1: 只使用最后一个时间步（推荐）
print("\n=== 方案1: 使用最后时间步特征 ===")
print("优点: 保持时间逻辑性，特征与标签时间匹配")

X_last = X[:, -1, :]  # 形状: (样本数, 特征数)
print(f"最后时间步数据形状: {X_last.shape}")

model1 = xgb.XGBClassifier(random_state=42, n_estimators=100)
model1.fit(X_last, y)

importance_df1 = pd.DataFrame({
    'feature': feature_names, 
    'importance': model1.feature_importances_
})
importance_df1.sort_values(by='importance', ascending=False, inplace=True)

print("特征重要性排序:")
print(importance_df1)

# 方案2: 统计聚合特征
print("\n=== 方案2: 统计聚合特征 ===")
print("优点: 利用整个时间序列信息，提取统计特征")

# 计算统计特征
X_mean = np.mean(X, axis=1)  # 时间序列均值
X_std = np.std(X, axis=1)    # 时间序列标准差
X_max = np.max(X, axis=1)    # 时间序列最大值
X_min = np.min(X, axis=1)    # 时间序列最小值

# 合并统计特征
X_stats = np.concatenate([X_mean, X_std, X_max, X_min], axis=1)
print(f"统计聚合数据形状: {X_stats.shape}")

# 创建统计特征名称
stat_feature_names = []
for stat in ['mean', 'std', 'max', 'min']:
    for feat in feature_names:
        stat_feature_names.append(f'{feat}_{stat}')

model2 = xgb.XGBClassifier(random_state=42, n_estimators=100)
model2.fit(X_stats, y)

importance_df2 = pd.DataFrame({
    'feature': stat_feature_names, 
    'importance': model2.feature_importances_
})
importance_df2.sort_values(by='importance', ascending=False, inplace=True)

print("前20个最重要的统计特征:")
print(importance_df2.head(20))

# 方案3: 错误的reshape方式（仅作对比）
print("\n=== 方案3: 错误的reshape方式（仅作对比说明）===")
print("问题: 破坏时间关系，标签不匹配，存在数据泄漏")

X_wrong = X.reshape(-1, X.shape[-1])
y_wrong = np.repeat(y, X.shape[1])
print(f"错误reshape数据形状: X={X_wrong.shape}, y={y_wrong.shape}")
print("这种方式的问题:")
print("1. 时间步被当作独立样本，失去时间连续性")
print("2. 中间时间步的特征被赋予最终时间步的标签")
print("3. 存在严重的数据泄漏问题")

print("\n=== 总结 ===")
print("推荐使用方案1（最后时间步）或方案2（统计聚合）")
print("避免使用方案3，因为它会破坏时间序列的逻辑结构")
